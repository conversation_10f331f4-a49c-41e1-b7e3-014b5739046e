import { defineConfig } from 'vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'
import fs from 'fs'
import path from 'path'

// 动态生成邮件模板列表的插件
function generateTemplatesPlugin() {
  
  const generateTemplates = () => {
    const templates = []
    const cbiDir = path.join(__dirname, 'public/cbi')
    
    if (!fs.existsSync(cbiDir)) {
      console.warn('CBI目录不存在:', cbiDir)
      return
    }
    
    // 递归读取目录
    function scanDirectory(dir, relativePath = '') {
      const items = fs.readdirSync(dir)
      
      items.forEach(item => {
        const fullPath = path.join(dir, item)
        const itemRelativePath = relativePath ? `${relativePath}/${item}` : item
        
        if (fs.statSync(fullPath).isDirectory()) {
          // 递归扫描子目录
          scanDirectory(fullPath, itemRelativePath)
        } else if (item.endsWith('.html')) {
          // 读取HTML文件的title
          try {
            const content = fs.readFileSync(fullPath, 'utf-8')
            const titleMatch = content.match(/<title>(.*?)<\/title>/i)
            
            if (titleMatch) {
              const title = titleMatch[1].trim()
              const templatePath = `/cbi/${itemRelativePath}`
              
              templates.push({
                name: title,
                path: templatePath
              })
            }
          } catch (error) {
            console.warn(`读取文件失败: ${fullPath}`, error.message)
          }
        }
      })
    }
    
    scanDirectory(cbiDir)
    
    // 按名称排序
    templates.sort((a, b) => a.name.localeCompare(b.name))
    
    // 生成templates.json文件
    const templatesPath = path.join(__dirname, 'src/templates.json')
    fs.writeFileSync(templatesPath, JSON.stringify(templates, null, 2))
    
    console.log(`生成了 ${templates.length} 个邮件模板`)
  }

  return {
    name: 'generate-templates',
    buildStart() {
      generateTemplates()
    },
    configureServer(server) {
      // 开发模式下也生成templates
      generateTemplates()
    }
  }
}

export default defineConfig({
  root: '.',
  build: {
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      }
    }
  },
  server: {
    port: 3000,
    open: true
  },
  plugins: [vue(), generateTemplatesPlugin()]
}) 