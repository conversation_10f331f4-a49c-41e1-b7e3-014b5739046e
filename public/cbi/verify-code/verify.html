<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
 <head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>验证码邮件模板</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <style type="text/css">
    body {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }
    table, td {
      border-collapse: collapse;
    }
    img {
      border: 0;
      height: auto;
      line-height: 100%;
      outline: none;
      text-decoration: none;
      -ms-interpolation-mode: bicubic;
    }
    p {
      display: block;
      margin: 13px 0;
    }
  </style>
  <style type="text/css">
    @media only screen and (max-width:480px) {
      @-ms-viewport {
        width: 320px;
      }
      @viewport {
        width: 320px;
      }
    }
  </style>
</head>
<body>
  <div style="max-width: 768px;background:#f7f8f7;margin:0px auto;">
    <div style="width: 100%;background:#fff;background-color:#fff;border-radius: 4px;overflow: hidden;">
      <table
        align="center"
        border="0"
        cellpadding="0"
        cellspacing="0"
        width="100%"
        style="background:#fff;background-color:#fff;width:100%;">
        <tbody>
          <tr>
            <td
              align="left"
              style="direction:ltr;font-size:0px;padding:30px 0px;text-align:left;">
              <img
                width="159"
                src="https://activity.easybank.com.cn/images/verify-code/logo.png"
                alt="logo">
            </td>
          </tr>
          <tr>
            <td align="center" style="">
              <table
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                width="100%"
                style="width:100%;">
                <tbody>
                  <tr>
                    <td
                      align='left'
                      style='font-size:0px;word-break:break-word;'>
                      <div style="
                      height: 110px;
                      background-image: url('https://activity.easybank.com.cn/images/verify-code/bg.0809.png');
                      background-size: cover;
                      font-size: 16px;
                      font-family: PingFangSC-Medium, PingFang SC;
                      font-weight: 500;
                      color: #FFFFFF;
                      line-height: 20px;">
                        <div style="padding: 50px 0 0 20px;">Verification Code</div>
                        <div style="margin-top: 5px;padding-left: 20px;">验证码</div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td
                      align='left'
                      style='font-size:0px;word-break:break-word;'>
                      <div style="
                        padding: 30px 20px 30px;">
                        <div style="
                          font-size: 16px;
                          font-family: PingFangSC-Medium, PingFang SC;
                          font-weight: 500;
                          color: #060432;
                          line-height: 24px;">
                          Dear [customer name]：
                        </div>
                        <div style="
                          margin-top: 25px;
                          font-size: 14px;
                          font-family: PingFangSC-Regular, PingFang SC;
                          font-weight: 400;
                          color: #333752;
                          line-height: 24px;">
                          Thank you for chosing CBiBank. Your verification code is 
                          <span style="
                            font-size: 24px;
                            color: #060432;">
                            946132
                          </span>
                          (valid within 10 minutes). To protect your account, do not forward this email or give this code to anyone. 
                        </div>
                        <div style="
                          margin-top: 25px;
                          font-size: 14px;
                          font-family: PingFangSC-Regular, PingFang SC;
                          font-weight: 400;
                          color: #333752;
                          line-height: 24px;">
                          The information contained in this e-mail message is intended only for the personal and confidential use of the recipient(s) named above. If you have received this communication in error, please notify us immediately by e-mail 
                          <a
                            style="
                            color: #4A6AFF;
                            text-decoration: none;" 
                            href="mailto:<EMAIL>">
                            <EMAIL>
                          </a>
                          or call customer service at ************, and delete the original message.
                        </div>
                        <div style="
                          margin-top: 20px;
                          font-size: 14px;
                          font-family: PingFangSC-Regular, PingFang SC;
                          font-weight: 400;
                          color: #5A607F;
                          line-height: 14px;">
                          CB International Bank
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td
                      align='left'
                      style='font-size:0px;word-break:break-word;'>
                      <div style="padding: 30px 20px 30px;background: #F6F7FC;">
                        <div style="
                          font-size: 16px;
                          font-family: PingFangSC-Medium, PingFang SC;
                          font-weight: 500;
                          color: #060432;
                          line-height: 24px;">
                          尊敬的{{customer name}}客户您好：
                        </div>
                        <div style="
                          margin-top: 25px;
                          font-size: 14px;
                          font-family: PingFangSC-Regular, PingFang SC;
                          font-weight: 400;
                          color: #333752;
                          line-height: 24px;">
                          感谢您使用CBiBank，您的验证码为
                          <span style="
                            font-size: 24px;
                            color: #060432;">
                            946132
                          </span>
                          （10分钟内有效）。为了保护您的账户，请勿转发此邮件或将此代码给任何人。
                        </div>
                        <div style="
                          margin-top: 25px;
                          font-size: 14px;
                          font-family: PingFangSC-Regular, PingFang SC;
                          font-weight: 400;
                          color: #333752;
                          line-height: 24px;">
                          本邮件所包含的信息仅用于上述收件人的个人使用，其所载内容可能含有保密信息。若您误收到本邮件，请立即通过电子邮件通知我们：
                          <a
                            style="
                            color: #4A6AFF;
                            text-decoration: none;" 
                            href="mailto:<EMAIL>">
                            <EMAIL>
                          </a>
                          ，或致电客户服务热线：************，并将原始邮件从系统中删除。
                        </div>
                        <div style="
                          margin-top: 20px;
                          font-size: 14px;
                          font-family: PingFangSC-Regular, PingFang SC;
                          font-weight: 400;
                          color: #5A607F;
                          line-height: 14px;">
                          CB International Bank
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</body>
</html>
