<template>
  <div class="container">
    <h1>HTML邮件模板预览</h1>
    <div class="stats-bar">
      <div class="total-count">
        总共 {{ totalTemplates }} 个模板
      </div>
      <div class="performance-tip">
        <el-icon><InfoFilled /></el-icon>
        <span>模板采用懒加载技术，滚动时自动加载</span>
      </div>
    </div>
    <input 
      type="text" 
      v-model="searchTerm" 
      class="search-box" 
      placeholder="搜索模板..."
      @input="handleSearch"
    >
    <div class="template-list">
      <VirtualScrollGrid
        :categorized-data="categorizedTemplates"
        :item-height="300"
        :category-height="70"
        :columns-per-row="3"
        :gap="30"
        :buffer-size="1"
      >
        <template #default="{ template }">
          <TemplateCard :template="template" />
        </template>
      </VirtualScrollGrid>
    </div>
    
    <!-- 搜索无结果时的提示 -->
    <div v-if="searchTerm && totalTemplates === 0" class="no-results-container">
      <el-icon><Search /></el-icon>
      <span>没有找到匹配的模板</span>
      <p>尝试使用不同的关键词搜索</p>
    </div>
  </div>
</template>

<script>
import TemplateCard from './TemplateCard.vue'
import VirtualScrollGrid from './VirtualScrollGrid.vue'
import templatesData from '../templates.json'
import { InfoFilled, Search } from '@element-plus/icons-vue'

export default {
  name: 'EmailTemplateList',
  components: {
    TemplateCard,
    VirtualScrollGrid,
    InfoFilled,
    Search
  },
  data() {
    return {
      searchTerm: '',
      templates: templatesData
    }
  },
  computed: {
    templatesWithCategories() {
      return this.templates.map(template => ({
        ...template,
        category: this.extractCategoryFromPath(template.path)
      }))
    },
    filteredTemplates() {
      if (!this.searchTerm) {
        return this.templatesWithCategories
      }
      const searchLower = this.searchTerm.toLowerCase()
      return this.templatesWithCategories.filter(template => 
        template.name.toLowerCase().includes(searchLower) ||
        template.path.toLowerCase().includes(searchLower) ||
        template.category.toLowerCase().includes(searchLower)
      )
    },
    categorizedTemplates() {
      const categories = {}
      this.filteredTemplates.forEach(template => {
        if (!categories[template.category]) {
          categories[template.category] = []
        }
        categories[template.category].push(template)
      })
      return categories
    },
    sortedCategories() {
      return Object.keys(this.categorizedTemplates).sort()
    },
    totalTemplates() {
      return this.filteredTemplates.length
    }
  },
  methods: {
    extractCategoryFromPath(path) {
      // 从路径中提取目录名作为分类
      // 例如: '/cbi/account-suspended/pb/index.html' -> 'account-suspended'
      const parts = path.split('/')
      if (parts.length >= 2 && parts[1] === 'cbi') {
        return parts[2]
      }
      return '其他'
    },
    handleSearch() {
      // 搜索处理已经通过computed属性自动处理
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.stats-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.total-count {
  font-weight: 600;
  color: #374151;
}

.performance-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.search-box {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 16px;
  box-sizing: border-box;
}

.template-list {
  margin-top: 20px;
  margin-bottom: 40px;
  height: calc(100vh - 300px); /* 为虚拟滚动容器设置固定高度 */
  min-height: 600px;
}

/* 移除原有的分类和网格样式，现在由VirtualScrollGrid组件处理 */

.no-results-container {
  text-align: center;
  padding: 60px 0;
  color: #6b7280;
}

.no-results-container .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-results-container span {
  display: block;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.no-results-container p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-bar {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .template-list {
    height: calc(100vh - 350px); /* 移动端调整高度 */
  }
}
</style> 