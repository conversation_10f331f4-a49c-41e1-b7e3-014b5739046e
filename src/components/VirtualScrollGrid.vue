<template>
  <div class="virtual-scroll-container" ref="containerRef" @scroll="handleScroll">
    <div class="virtual-scroll-content" :style="{ height: totalHeight + 'px' }">
      <!-- 渲染可见的分类和项目 -->
      <div
        v-for="item in visibleItems"
        :key="item.key"
        :style="{
          position: 'absolute',
          top: item.top + 'px',
          left: item.left + 'px',
          width: item.width + 'px',
          height: item.height + 'px'
        }"
        :class="item.type"
      >
        <!-- 分类标题 -->
        <div v-if="item.type === 'category'" class="category-title">
          {{ item.data.category }} ({{ item.data.count }})
        </div>
        
        <!-- 模板卡片 -->
        <div v-else-if="item.type === 'template'" class="template-wrapper">
          <slot :template="item.data" :index="item.index"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

export default {
  name: 'VirtualScrollGrid',
  props: {
    // 分类数据，格式：{ categoryName: [templates...] }
    categorizedData: {
      type: Object,
      required: true
    },
    // 每个模板卡片的高度
    itemHeight: {
      type: Number,
      default: 280 // 卡片高度 + padding
    },
    // 分类标题高度
    categoryHeight: {
      type: Number,
      default: 60
    },
    // 每行显示的列数（响应式）
    columnsPerRow: {
      type: Number,
      default: 3
    },
    // 网格间距
    gap: {
      type: Number,
      default: 20
    },
    // 额外的缓冲区域（屏幕高度的倍数）
    bufferSize: {
      type: Number,
      default: 1
    }
  },
  setup(props) {
    const containerRef = ref(null)
    const scrollTop = ref(0)
    const containerHeight = ref(0)
    const containerWidth = ref(0)
    
    // 计算每个项目的位置和尺寸信息
    const layoutItems = computed(() => {
      const items = []
      let currentTop = 0
      const columns = responsiveColumns.value
      const itemWidth = (containerWidth.value - (columns - 1) * props.gap) / columns
      
      Object.keys(props.categorizedData).forEach(category => {
        const templates = props.categorizedData[category]
        
        // 添加分类标题
        items.push({
          key: `category-${category}`,
          type: 'category',
          top: currentTop,
          left: 0,
          width: containerWidth.value,
          height: props.categoryHeight,
          data: { category, count: templates.length }
        })
        
        currentTop += props.categoryHeight
        
        // 添加模板项目
        templates.forEach((template, index) => {
          const row = Math.floor(index / columns)
          const col = index % columns
          
          items.push({
            key: `template-${template.path}`,
            type: 'template',
            top: currentTop + row * props.itemHeight,
            left: col * (itemWidth + props.gap),
            width: itemWidth,
            height: props.itemHeight,
            data: template,
            index: index
          })
        })
        
        // 计算这个分类占用的总行数
        const totalRows = Math.ceil(templates.length / columns)
        currentTop += totalRows * props.itemHeight + 30 // 分类间距
      })
      
      return items
    })
    
    // 计算总高度
    const totalHeight = computed(() => {
      if (layoutItems.value.length === 0) return 0
      const lastItem = layoutItems.value[layoutItems.value.length - 1]
      return lastItem.top + lastItem.height
    })
    
    // 计算可见的项目
    const visibleItems = computed(() => {
      const buffer = containerHeight.value * props.bufferSize
      const startY = scrollTop.value - buffer
      const endY = scrollTop.value + containerHeight.value + buffer
      
      return layoutItems.value.filter(item => {
        return item.top + item.height >= startY && item.top <= endY
      })
    })
    
    // 处理滚动事件
    const handleScroll = () => {
      if (containerRef.value) {
        scrollTop.value = containerRef.value.scrollTop
      }
    }
    
    // 更新容器尺寸
    const updateContainerSize = () => {
      if (containerRef.value) {
        containerHeight.value = containerRef.value.clientHeight
        containerWidth.value = containerRef.value.clientWidth
      }
    }
    
    // 响应式计算列数
    const responsiveColumns = computed(() => {
      if (containerWidth.value < 768) return 1 // 移动端
      if (containerWidth.value < 1200) return 2 // 平板
      return props.columnsPerRow // 桌面端使用传入的列数
    })

    // 响应式监听
    const resizeObserver = ref(null)

    onMounted(() => {
      updateContainerSize()

      // 监听容器尺寸变化
      if (window.ResizeObserver) {
        resizeObserver.value = new ResizeObserver(() => {
          updateContainerSize()
        })
        resizeObserver.value.observe(containerRef.value)
      }

      // 监听窗口尺寸变化
      window.addEventListener('resize', updateContainerSize)
    })
    
    onUnmounted(() => {
      if (resizeObserver.value) {
        resizeObserver.value.disconnect()
      }
      window.removeEventListener('resize', updateContainerSize)
    })
    
    // 监听数据变化，重置滚动位置
    watch(() => props.categorizedData, () => {
      nextTick(() => {
        if (containerRef.value) {
          containerRef.value.scrollTop = 0
          scrollTop.value = 0
        }
      })
    })
    
    return {
      containerRef,
      visibleItems,
      totalHeight,
      handleScroll,
      responsiveColumns
    }
  }
}
</script>

<style scoped>
.virtual-scroll-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.virtual-scroll-content {
  position: relative;
  width: 100%;
}

.category-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 2px solid #007bff;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.category {
  width: 100%;
}

.template-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.template-wrapper > * {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
</style>
