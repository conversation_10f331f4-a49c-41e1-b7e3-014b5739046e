# HTML邮件模板预览工具

这是一个基于Vite的HTML邮件模板预览工具，用于预览`cbi/`目录下的所有HTML邮件模板。

## 功能特性

- 🔍 支持模板搜索和分类浏览
- 📱 响应式设计，支持移动端预览
- 🚀 基于Vite，热更新支持
- 📂 自动发现所有HTML文件
- 🎨 美观的界面设计

## 安装和使用

1. 安装依赖
```bash
pnpm install
```

2. 启动开发服务器
```bash
pnpm run dev
```

3. 在浏览器中打开 `http://localhost:3000`

## 目录结构

```
html-email/
├── cbi/                    # 邮件模板目录
│   ├── account-transfer/   # 账户转移模板
│   ├── bingocard/          # BingoCard相关模板
│   ├── fund/               # 基金相关模板
│   └── ...                 # 其他模板目录
├── package.json            # 项目依赖配置
├── vite.config.js          # Vite配置文件
└── index.html              # 主页面
```

## 脚本命令

- `pnpm run dev` - 启动开发服务器
- `pnpm run build` - 构建生产版本
- `pnpm run preview` - 预览构建版本

## 模板管理

所有邮件模板都位于 `cbi/` 目录下，按功能分类组织：

- 📧 基础模板
- 👤 账户管理
- 🎯 活动推广
- 💳 卡片服务
- 💰 费用管理
- 🔐 安全认证
- 📊 基金投资
- 等等...

## 使用说明

1. 打开主页面，可以看到所有模板按类别分组显示
2. 使用顶部搜索框快速查找特定模板
3. 点击模板卡片中的"预览"按钮在新窗口中查看模板
4. 所有模板都支持实时预览，无需重新启动服务器

## 技术栈

- **Vite** - 现代前端构建工具
- **HTML/CSS/JavaScript** - 原生技术栈
- **响应式设计** - 支持各种设备预览 